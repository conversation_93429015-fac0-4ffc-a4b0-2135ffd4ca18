class LicenseApp {
    constructor() {
        this.licenses = [];
        this.s3Files = [];
        this.isScrolling = false;
        this.init();
    }

    init() {
        this.setupEvents();
        this.loadData();
    }

    // 事件设置
    setupEvents() {
        // 标签页切换
        document.querySelectorAll('.tab-btn').forEach(btn => 
            btn.onclick = () => this.switchTab(btn.dataset.tab)
        );

        // 表单提交
        const forms = [
            ['generateForm', '/api/generate', 'generateResult'],
            ['batchGenerateForm', '/api/batch-generate', 'batchResult'],
            ['configForm', '/api/config', 'configResult']
        ];
        forms.forEach(([id, url, resultId]) => {
            const form = this.$(id);
            if (form) {
                form.onsubmit = (e) => {
                    e.preventDefault();
                    this.apiCall(url, Object.fromEntries(new FormData(e.target)), resultId, null, e.target);
                };
            }
        });

        // 搜索过滤
        ['searchInput', 'typeFilter', 'timeFilter'].forEach(id => {
            const el = this.$(id);
            if (el) el.oninput = () => this.filterLicenses();
        });

        // 其他事件
        const groupEl = this.$('groupByType');
        if (groupEl) groupEl.onchange = () => this.renderLicenses();
        
        document.addEventListener('change', (e) => {
            if (e.target.type === 'checkbox' && e.target.name === 'selectLicense') {
                this.updateBatchControls();
            }
        });
    }

    // 标签页切换
    switchTab(tabId) {
        this.$$('.tab-btn, .tab-content').forEach(el => el.classList.remove('active'));
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
        this.$(tabId).classList.add('active');
    }

    // 统一的API调用
    async apiCall(url, data = {}, resultId = null, statusId = null, form = null) {
        if (statusId) this.setStatus(statusId, '正在处理...');

        try {
            const options = {
                method: data === null ? 'GET' : 'POST',
                headers: { 'Content-Type': 'application/json' }
            };
            if (data !== null) options.body = JSON.stringify(data);

            const response = await fetch(url, options);
            const result = await response.json();
            
            if (resultId) this.showResult(resultId, result);
            if (result.success && form) {
                form.reset();
                this.loadData();
            }
            
            return result;
        } catch (error) {
            const errorResult = { success: false, error: error.message };
            if (resultId) this.showResult(resultId, errorResult);
            return errorResult;
        } finally {
            if (statusId) this.setStatus(statusId, '');
        }
    }

    // 统一的HTTP请求方法
    async httpRequest(url, method = 'GET', data = null) {
        try {
            const options = { method, headers: { 'Content-Type': 'application/json' } };
            if (data) options.body = JSON.stringify(data);

            const response = await fetch(url, options);
            return await response.json();
        } catch (error) {
            console.error('请求失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 简化的数据获取
    async getData(url) {
        return this.httpRequest(url, 'GET');
    }

    // 数据加载
    async loadData() {
        await Promise.all([this.loadLicenses(), this.loadConfig()]);
    }

    async loadLicenses() {
        const result = await this.getData('/api/licenses');
        if (result.success) {
            this.licenses = result.licenses;
            this.refresh();
        }
    }

    async loadConfig() {
        const result = await this.getData('/api/config');
        if (result.success) {
            const config = result.config;
            // 填充配置表单
            ['accessKey', 'secretKey', 'bucket', 'region', 'endpoint', 'encryptionKey'].forEach(key => {
                const el = this.$('config' + key.charAt(0).toUpperCase() + key.slice(1));
                if (el && config[key]) el.value = config[key];
            });
        }
    }

    // 界面刷新
    refresh() {
        this.updateStats();
        this.renderLicenses();
    }

    // 统计更新
    updateStats() {
        const stats = { total: 0, dragon: 0, annual: 0, trial: 0 };
        this.licenses.forEach(license => {
            stats.total++;
            stats[license.licenseType] = (stats[license.licenseType] || 0) + 1;
        });
        Object.keys(stats).forEach(key => this.setElementText(key + 'Count', stats[key]));
    }

    // 激活码渲染
    renderLicenses() {
        const container = this.$('licensesList');
        if (!container) return;

        if (!this.licenses.length) {
            container.innerHTML = '<div class="empty-state">暂无数据</div>';
            return;
        }

        const filtered = this.getFilteredLicenses();
        const groupByType = this.$('groupByType')?.checked;
        container.innerHTML = groupByType ? this.renderGrouped(filtered) : this.renderFlat(filtered);
    }

    renderFlat(licenses) {
        return licenses.map(license => this.renderLicenseItem(license)).join('');
    }

    renderGrouped(licenses) {
        const groups = this.groupLicensesByType(licenses);
        return Object.keys(groups).map(type => {
            const items = groups[type];
            return `
                <div class="group-header">
                    <span>${this.getTypeName(type)} (${items.length})</span>
                </div>
                ${items.map(license => this.renderLicenseItem(license)).join('')}
            `;
        }).join('');
    }

    renderLicenseItem(license) {
        const licenseId = license.userId;
        const keyDisplay = license.key ? `${license.key.substring(0, 8)}...` : '无';
        const realStatus = this.getActivationStatus(license);  // 🔥 使用新的状态判断
        const statusBtn = realStatus === 'active'
            ? `<button class="btn btn-used btn-sm" onclick="App.markAsUsed('${licenseId}')">已激活</button>`
            : `<button class="btn btn-activate btn-sm" onclick="App.markAsActive('${licenseId}')">未使用</button>`;

        return `
            <div class="list-item" data-license-id="${licenseId}" style="grid-template-columns: 50px 2.5fr 1fr 1fr 1.5fr 1.5fr 1fr 2fr;">
                <div><input type="checkbox" name="selectLicense" value="${licenseId}" onchange="App.updateBatchControls()"></div>
                <div>
                    <div class="item-code">${license.code}</div>
                    <div class="item-meta">${license.userName} (${license.userId})</div>
                </div>
                <div><span class="badge badge-${license.licenseType}">${this.getTypeName(license.licenseType)}</span></div>
                <div>设备: ${license.maxDevices}</div>
                <div>密钥: ${keyDisplay}</div>
                <div>${license.notes || '无备注'}</div>
                <div>${this.getStatusBadge(license)}</div>
                <div class="actions">
                    ${statusBtn}
                    <button class="btn btn-delete btn-sm" onclick="App.deleteLicense('${licenseId}')">删除</button>
                    <button class="btn btn-copy btn-sm" onclick="App.copyToClipboard('${license.code}')" title="复制激活码">复制</button>
                </div>
            </div>
        `;
    }

    // 工具方法
    getTypeName(type) {
        const names = { dragon: '恶龙会员', annual: '年付会员', trial: '体验会员' };
        return names[type] || type;
    }

    groupLicensesByType(licenses) {
        return licenses.reduce((groups, license) => {
            const type = license.licenseType;
            if (!groups[type]) groups[type] = [];
            groups[type].push(license);
            return groups;
        }, {});
    }

    getFilteredLicenses() {
        const search = this.$('searchInput')?.value.toLowerCase() || '';
        const typeFilter = this.$('typeFilter')?.value || '';
        const timeFilter = this.$('timeFilter')?.value || '';

        return this.licenses.filter(license => {
            if (search && !license.code.toLowerCase().includes(search) &&
                !license.userName.toLowerCase().includes(search)) return false;
            if (typeFilter && license.licenseType !== typeFilter) return false;
            if (timeFilter) {
                const date = new Date(license.createdAt);
                const now = new Date();
                const diffDays = (now - date) / (1000 * 60 * 60 * 24);
                const timeFilters = { today: 1, week: 7, month: 30 };
                if (diffDays > timeFilters[timeFilter]) return false;
            }
            return true;
        });
    }

    // DOM工具方法
    $(id) { return document.getElementById(id); }
    $$(selector) { return document.querySelectorAll(selector); }

    setElementText(id, text) {
        const el = this.$(id);
        if (el) el.textContent = text;
    }

    showResult(resultId, result) {
        const el = this.$(resultId);
        if (!el) return;

        el.style.display = 'block';
        el.className = `result-panel result-${result.success ? 'success' : 'error'}`;
        el.textContent = result.message || result.error || (result.success ? '操作成功' : '操作失败');
    }

    setStatus(statusId, message, type = 'info') {
        const el = this.$(statusId);
        if (el) {
            el.textContent = message;
            el.className = `cloud-status ${type}`;
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);
        setTimeout(() => document.body.removeChild(toast), 3000);
    }

    // 连接测试
    testConnection() {
        this.apiCall('/api/test-connection', {}, 'testResult');
    }

    // 激活码操作
    async deleteLicense(identifier) {
        // 支持通过userId或code删除
        const license = this.licenses.find(l => l.userId === identifier || l.code === identifier);
        if (!license || !confirm(`确定删除 "${license.code}"？`)) return;

        const result = await this.httpRequest(`/api/license/${license.userId}`, 'DELETE');
        if (result.success) {
            this.licenses = this.licenses.filter(l => l.userId !== license.userId);
            this.refresh();
            this.showToast('删除成功', 'success');
        } else {
            this.showToast(result.error || '删除失败', 'error');
        }
    }

    async updateLicenseStatus(identifier, status) {
        const endpoints = { used: '/api/mark-used', active: '/api/mark-active' };
        const messages = { used: '标记成功', active: '激活成功' };

        // 支持通过userId或code查找
        const license = this.licenses.find(l => l.userId === identifier || l.code === identifier);
        if (!license) return;

        const result = await this.apiCall(endpoints[status], { licenseId: license.userId });
        if (result.success) {
            license.status = status;
            this.renderLicenses();
            this.showToast(messages[status], 'success');
        }
    }

    markAsUsed(licenseId) { return this.updateLicenseStatus(licenseId, 'used'); }
    markAsActive(licenseId) { return this.updateLicenseStatus(licenseId, 'active'); }

    // 批量操作
    getSelectedIds() {
        return Array.from(this.$$('input[name="selectLicense"]:checked')).map(cb => cb.value);
    }

    updateBatchControls() {
        const selected = this.getSelectedIds();
        const controls = this.$('batchControls');
        const count = this.$('selectedCount');

        if (controls) controls.style.display = selected.length ? 'flex' : 'none';
        if (count) count.textContent = `已选择 ${selected.length} 项`;
    }

    async batchDelete() {
        const ids = this.getSelectedIds();
        if (!ids.length || !confirm(`确定删除 ${ids.length} 个激活码？`)) return;

        const result = await this.apiCall('/api/batch-delete', { ids });
        if (result.success) {
            this.licenses = this.licenses.filter(l => !ids.includes(l.userId));
            this.refresh();
            this.clearSelection();
            this.showToast(`删除了 ${ids.length} 个激活码`, 'success');
        }
    }

    async batchUpdateStatus(status) {
        const ids = this.getSelectedIds();
        if (!ids.length) return;

        const statusNames = { used: '已用', active: '激活' };
        const result = await this.apiCall('/api/batch-update-status', { ids, status });
        if (result.success) {
            ids.forEach(id => {
                const license = this.licenses.find(l => l.userId === id);
                if (license) license.status = status;
            });
            this.renderLicenses();
            this.clearSelection();
            this.showToast(`标记了 ${ids.length} 个激活码为${statusNames[status]}`, 'success');
        }
    }

    batchMarkUsed() { return this.batchUpdateStatus('used'); }
    batchMarkActive() { return this.batchUpdateStatus('active'); }

    clearSelection() {
        this.$$('input[name="selectLicense"]:checked').forEach(cb => cb.checked = false);
        this.updateBatchControls();
    }

    // 复制功能
    copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showToast('已复制到剪贴板', 'success');
            }).catch(() => {
                this.fallbackCopy(text);
            });
        } else {
            this.fallbackCopy(text);
        }
    }

    fallbackCopy(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            this.showToast('已复制到剪贴板', 'success');
        } catch (err) {
            this.showToast('复制失败，请手动复制', 'error');
        }
        document.body.removeChild(textArea);
    }

    // 筛选功能
    filterLicenses() {
        this.renderLicenses();
    }

    refreshLicenses() {
        this.loadLicenses();
    }

    // 云端存储功能
    uploadToS3() {
        this.apiCall('/api/upload-qiniu', {}, 's3Result', '上传');
    }

    downloadFromS3() {
        this.apiCall('/api/download-qiniu', {}, 's3Result', '下载');
    }

    // 🔥 智能同步 - 简洁高效
    async syncFromS3() {
        if (!confirm('确定要智能同步云端激活码吗？\n将会：\n1. 下载新的激活码\n2. 更新激活状态\n3. 保持数据一致性')) return;

        const result = await this.apiCall('/api/download-qiniu', {}, 's3Result', '智能同步');
        if (result.success) {
            this.loadData(); // 刷新本地数据
            this.refreshS3List(); // 刷新云端数据
            this.showToast('智能同步完成！', 'success');
        }
    }

    async refreshS3List() {
        const result = await this.getData('/api/s3/list');
        if (result.success) {
            this.s3Files = result.files || [];
            this.renderCompareView();
            this.updateS3Stats();
            const s3Count = this.s3Files.filter(f => f.Key.endsWith('.dat')).length;
            this.showToast(`获取到 ${s3Count} 个云端激活码`, 'success');
        } else {
            this.showToast(result.error || 'S3列表获取失败', 'error');
        }
    }

    // 比较界面
    renderCompareView() {
        const localContainer = this.$('localFilesList');
        const s3Container = this.$('s3FilesList');
        if (!localContainer || !s3Container) return;

        const localCodes = this.licenses.map(l => l.code);
        const s3Codes = this.s3Files.filter(f => f.Key.endsWith('.dat')).map(f => f.Key.replace('.dat', ''));
        let allCodes = [...new Set([...localCodes, ...s3Codes])].sort();

        // 应用筛选
        allCodes = this.applyCompareFilters(allCodes, localCodes, s3Codes);

        localContainer.innerHTML = this.renderAlignedList(allCodes, localCodes, 'local');
        s3Container.innerHTML = this.renderAlignedList(allCodes, s3Codes, 's3');

        this.updateCompareStats(localCodes, s3Codes, allCodes);
    }

    renderAlignedList(allCodes, currentCodes, type) {
        if (!allCodes.length) return '<div class="loading">暂无数据</div>';

        const localCodes = this.licenses.map(l => l.code);
        const s3Codes = this.s3Files.filter(f => f.Key.endsWith('.dat')).map(f => f.Key.replace('.dat', ''));

        return allCodes.map(code => {
            const exists = currentCodes.includes(code);
            const license = this.licenses.find(l => l.code === code);
            const meta = license ? `${license.licenseType} | ${license.userName}` : '';

            if (!exists) {
                return `
                    <div class="compare-item empty">
                        <div>
                            <div class="compare-code">--- 空位 ---</div>
                            <div class="compare-meta">此处无激活码</div>
                        </div>
                        <div class="compare-actions">
                            ${type === 'local' && s3Codes.includes(code) ? `<button class="btn btn-sm" onclick="App.downloadS3File('${code}.dat')">下载</button>` : ''}
                            ${type === 's3' && localCodes.includes(code) ? `<button class="btn btn-sm" onclick="App.uploadSingleCode('${code}')">上传</button>` : ''}
                        </div>
                    </div>
                `;
            }

            const otherSideExists = type === 'local' ? s3Codes.includes(code) : localCodes.includes(code);
            const cssClass = otherSideExists ? 'same' : 'missing';

            return `
                <div class="compare-item ${cssClass}">
                    <div>
                        <div class="compare-code">${code}</div>
                        ${meta ? `<div class="compare-meta">${meta}</div>` : ''}
                    </div>
                    <div class="compare-actions">
                        <button class="btn btn-copy btn-sm" onclick="App.copyCode('${code}')">复制</button>
                        ${type === 'local' ? `<button class="btn btn-secondary btn-sm" onclick="App.deleteLicense('${code}')">删除</button>` : ''}
                        ${type === 'local' && !s3Codes.includes(code) ? `<button class="btn btn-sm" onclick="App.uploadSingleCode('${code}')">上传</button>` : ''}
                        ${type === 's3' ? `<button class="btn btn-sm" onclick="App.downloadS3File('${code}.dat')">下载</button>` : ''}
                        ${type === 's3' ? `<button class="btn btn-secondary btn-sm" onclick="App.deleteS3File('${code}.dat')">删除</button>` : ''}
                    </div>
                </div>
            `;
        }).join('');
    }

    // 比较筛选
    applyCompareFilters(allCodes, localCodes, s3Codes) {
        const searchTerm = this.$('compareSearch')?.value.toLowerCase() || '';
        const typeFilter = this.$('compareTypeFilter')?.value || 'all';
        const statusFilter = this.$('compareStatusFilter')?.value || 'all';

        return allCodes.filter(code => {
            if (searchTerm && !code.toLowerCase().includes(searchTerm)) return false;

            if (typeFilter !== 'all') {
                const license = this.licenses.find(l => l.code === code);
                if (!license || license.licenseType !== typeFilter) return false;
            }

            if (statusFilter !== 'all') {
                const inLocal = localCodes.includes(code);
                const inS3 = s3Codes.includes(code);
                const statusMap = {
                    both: inLocal && inS3,
                    'local-only': inLocal && !inS3,
                    's3-only': !inLocal && inS3,
                    missing: !inLocal || !inS3
                };
                return statusMap[statusFilter];
            }

            return true;
        });
    }

    filterCompareList() {
        this.renderCompareView();
    }

    clearCompareFilters() {
        ['compareSearch', 'compareTypeFilter', 'compareStatusFilter'].forEach(id => {
            const el = this.$(id);
            if (el) el.value = el.tagName === 'SELECT' ? 'all' : '';
        });
        this.renderCompareView();
    }

    // 统计更新
    updateS3Stats() {
        const s3Count = this.s3Files.filter(f => f.Key.endsWith('.dat')).length;
        this.setElementText('s3FileCount', s3Count);
        this.setElementText('localFileCount', this.licenses.length);
    }

    updateCompareStats(localCodes, s3Codes, filteredCodes) {
        const filteredLocalCodes = filteredCodes.filter(code => localCodes.includes(code));
        const filteredS3Codes = filteredCodes.filter(code => s3Codes.includes(code));

        const localMissing = filteredCodes.filter(code => !localCodes.includes(code)).length;
        const s3Missing = filteredCodes.filter(code => !s3Codes.includes(code)).length;

        this.setElementText('localTotalCount', filteredLocalCodes.length);
        this.setElementText('localMissingCount', localMissing);
        this.setElementText('s3TotalCount', filteredS3Codes.length);
        this.setElementText('s3MissingCount', s3Missing);

        this.updateCompareTypeStats(filteredLocalCodes, filteredS3Codes);
    }

    updateCompareTypeStats(localCodes, s3Codes) {
        const localStats = this.calculateTypeStats(localCodes);
        const s3Stats = this.calculateTypeStats(s3Codes);

        const localEl = this.$('localStatsTypes');
        const s3El = this.$('s3StatsTypes');
        if (localEl) localEl.innerHTML = this.renderTypeStats(localStats);
        if (s3El) s3El.innerHTML = this.renderTypeStats(s3Stats);
    }

    calculateTypeStats(codes) {
        const stats = { dragon: 0, annual: 0, trial: 0, unknown: 0 };
        codes.forEach(code => {
            const license = this.licenses.find(l => l.code === code);
            if (license) {
                stats[license.licenseType] = (stats[license.licenseType] || 0) + 1;
            } else {
                stats.unknown++;
            }
        });
        return stats;
    }

    renderTypeStats(stats) {
        const typeNames = { dragon: '龙年版', annual: '年度版', trial: '试用版', unknown: '未知' };
        return Object.entries(stats)
            .filter(([, count]) => count > 0)
            .map(([type, count]) => `<span>${typeNames[type]}: <strong>${count}</strong></span>`)
            .join('');
    }

    // 同步滚动
    syncScroll(source, targetId) {
        if (this.isScrolling) return;
        this.isScrolling = true;

        const target = this.$(targetId);
        if (target) target.scrollTop = source.scrollTop;

        setTimeout(() => { this.isScrolling = false; }, 10);
    }

    // S3文件操作
    async uploadSingleCode(code) {
        if (!confirm(`上传激活码 "${code}" 到云端？`)) return;

        const license = this.licenses.find(l => l.code === code);
        if (!license) {
            this.showToast('激活码不存在', 'error');
            return;
        }

        const result = await this.apiCall('/api/upload-qiniu', { singleCode: code });
        if (result.success) {
            this.showToast(`${code} 上传成功`, 'success');
            this.refreshS3List();
        }
    }

    async downloadS3File(key) {
        if (!confirm(`下载 "${key}"？`)) return;

        const result = await this.getData(`/api/s3/download/${encodeURIComponent(key)}`);
        if (result.success) {
            this.showToast(`${key} 下载成功`, 'success');
            this.loadData();
            this.refreshS3List();
        } else {
            this.showToast(result.error || '下载失败', 'error');
        }
    }

    async deleteS3File(key) {
        if (!confirm(`删除云端文件 "${key}"？`)) return;

        const result = await this.httpRequest(`/api/s3/delete/${encodeURIComponent(key)}`, 'DELETE');
        if (result.success) {
            this.showToast(`${key} 删除成功`, 'success');
            this.refreshS3List();
        } else {
            this.showToast(result.error || '删除失败', 'error');
        }
    }

    // 复制激活码
    copyCode(code) {
        this.copyToClipboard(code);
    }

    // 🔥 获取激活状态 - 简洁高效
    getActivationStatus(license) {
        // 直接使用 status 字段判断
        return license.status === 'active' ? 'active' : 'unused';
    }

    // 🔥 获取状态显示标签
    getStatusBadge(license) {
        const status = this.getActivationStatus(license);
        if (status === 'active') {
            // 检查是否过期
            if (license.activatedAt && license.activatedAt > 0 && license.activatedAt < Date.now()) {
                return '<span class="badge badge-expired">已过期</span>';
            }
            return '<span class="badge badge-active">已激活</span>';
        }
        return '<span class="badge badge-unused">未使用</span>';
    }

    // 配置相关
    updateS3Config() {
        const provider = this.$('s3Provider')?.value;
        const configs = {
            qiniu: { endpoint: 'https://s3.cn-south-1.qiniucs.com', region: 'cn-south-1' },
            aliyun: { endpoint: 'https://oss-cn-hangzhou.aliyuncs.com', region: 'cn-hangzhou' },
            tencent: { endpoint: 'https://cos.ap-beijing.myqcloud.com', region: 'ap-beijing' },
            aws: { endpoint: 'https://s3.amazonaws.com', region: 'us-east-1' },
            minio: { endpoint: 'http://localhost:9000', region: 'us-east-1' }
        };

        if (configs[provider]) {
            const endpointEl = this.$('configEndpoint');
            const regionEl = this.$('configRegion');
            if (endpointEl) endpointEl.value = configs[provider].endpoint;
            if (regionEl) regionEl.value = configs[provider].region;
        }
    }

    // 导出功能
    showExportDialog() {
        const dialog = this.$('exportDialog');
        if (dialog) {
            const filtered = this.getFilteredLicenses();
            this.setElementText('exportCount', filtered.length);
            dialog.style.display = 'flex';
        }
    }

    hideExportDialog() {
        const dialog = this.$('exportDialog');
        if (dialog) dialog.style.display = 'none';
    }

    exportLicenses() {
        const format = this.$('exportFormat')?.value || 'txt';
        const filtered = this.getFilteredLicenses();

        const formatters = {
            csv: () => 'Code,User,Type,Status,Created\n' +
                filtered.map(l => `${l.code},${l.userName},${l.licenseType},${l.status},${l.createdAt}`).join('\n'),
            json: () => JSON.stringify(filtered, null, 2),
            txt: () => filtered.map(l => l.code).join('\n')
        };

        const content = (formatters[format] || formatters.txt)();
        const filename = `licenses_${new Date().toISOString().split('T')[0]}.${format}`;

        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);

        this.hideExportDialog();
        this.showToast(`导出了 ${filtered.length} 个激活码`, 'success');
    }
}

// 全局实例
const App = new LicenseApp();
