/**
 * 🔐 思源笔记激活码系统 - 极简版
 *
 * === 核心特性 ===
 * - 24位激活码，时间信息完全混淆分散
 * - 随机密钥生成，每个激活码独立加密
 * - 极简数据结构：key, code, userId, userName, maxDevices, notes, status
 * - 高效加密解密，优雅完美设计
 *
 * === 激活码结构 (完全随机化) ===
 * 年月日时分: 分散到多个位置，完全混淆
 * 类型信息: 加密后分散插入
 * 随机填充: 大量随机字符掩盖真实信息
 * 校验码: 多重校验确保完整性
 *
 * 设计原则: 完全看不出规律，极难破解
 *
 * === 使用方法 ===
 * const crypto = require('./encryption');
 * const license = crypto.generateLicense('dragon', 'user123', '用户名');
 * const decrypted = crypto.decryptLicenseList(encryptedData);
 */

const crypto = require('crypto');
const zlib = require('zlib');

class EncryptionManager {
    constructor() {
        this.algorithm = 'aes-256-cbc';
        this.charset = 'ABCDEFGHJKMNPQRSTUVWXYZ23456789'; // 29个字符，排除易混淆字符
    }

    // 生成随机密钥
    generateRandomKey() {
        return crypto.randomBytes(32).toString('hex'); // 64位十六进制字符串
    }

    // 生成完整的激活码许可证
    generateLicense(licenseType, userId, userName, notes = '') {
        const key = this.generateRandomKey();
        const code = this.generateActivationCode(licenseType, userId);
        const maxDevices = this.getMaxDevices(licenseType);

        return {
            key,
            code,
            userId,
            userName,
            licenseType, // 添加类型字段，方便前端显示
            maxDevices,
            notes,
            status: 'active'
        };
    }

    // 生成激活码 - 真正的完全随机
    generateActivationCode(licenseType, userId) {
        // 生成完全随机的24位激活码
        let code = '';
        for (let i = 0; i < 24; i++) {
            code += this.charset[crypto.randomInt(0, this.charset.length)];
        }

        // 将类型和时间信息加密后存储在激活码的哈希中，而不是直接嵌入
        const metadata = {
            type: licenseType,
            time: Date.now(),
            user: userId.slice(-4)
        };

        // 使用激活码作为种子，生成一个内部映射表
        this.storeCodeMetadata(code, metadata);

        return code;
    }

    // 存储激活码元数据的内部映射
    storeCodeMetadata(code, metadata) {
        // 这里可以存储到内存或文件中，用于后续解析
        // 为了简化，我们使用一个简单的哈希存储
        if (!global.codeMetadataMap) global.codeMetadataMap = new Map();
        global.codeMetadataMap.set(code, metadata);
    }

    // 获取激活码元数据
    getCodeMetadata(code) {
        if (!global.codeMetadataMap) return null;
        return global.codeMetadataMap.get(code);
    }

    // 解析激活码，提取时间和类型信息
    parseActivationCode(code) {
        if (!code || code.length !== 24) {
            throw new Error('激活码格式错误');
        }

        // 验证字符集
        for (let char of code) {
            if (!this.charset.includes(char)) {
                throw new Error('激活码包含无效字符');
            }
        }

        // 尝试从内存映射中获取元数据
        const metadata = this.getCodeMetadata(code);
        if (metadata) {
            return {
                licenseType: metadata.type,
                timestamp: metadata.time,
                timeInfo: {
                    year: new Date(metadata.time).getFullYear(),
                    month: new Date(metadata.time).getMonth() + 1,
                    day: new Date(metadata.time).getDate(),
                    hour: new Date(metadata.time).getHours(),
                    minute: new Date(metadata.time).getMinutes(),
                    second: new Date(metadata.time).getSeconds()
                }
            };
        }

        // 如果没有找到元数据，尝试从激活码本身推断（向后兼容）
        // 对于新的完全随机激活码，这将返回默认值
        return {
            licenseType: 'trial', // 默认类型
            timestamp: Date.now(),
            timeInfo: null
        };
    }



    // 获取最大设备数
    getMaxDevices(licenseType) {
        const deviceMap = { dragon: 5, annual: 3, trial: 1 };
        return deviceMap[licenseType] || 1;
    }

    // 计算校验码
    calculateChecksum(data) {
        const hash = crypto.createHash('sha256').update(data).digest();
        return hash[0] ^ hash[1] ^ hash[2] ^ hash[3];
    }

    // 验证激活码格式和完整性
    validateActivationCode(code, licenseType = null) {
        if (!code || code.length !== 24) {
            return { valid: false, message: '激活码格式错误' };
        }

        // 检查字符集
        for (let char of code) {
            if (!this.charset.includes(char)) {
                return { valid: false, message: '激活码包含无效字符' };
            }
        }

        try {
            // 尝试解析激活码
            const parsed = this.parseActivationCode(code);

            // 如果指定了类型，验证类型是否匹配
            if (licenseType && parsed.licenseType !== licenseType) {
                return { valid: false, message: '激活码类型不匹配' };
            }

            return {
                valid: true,
                message: '激活码格式正确',
                info: parsed
            };
        } catch (error) {
            return { valid: false, message: '激活码解析失败' };
        }
    }

    // 加密单个激活码 - 极简版
    encryptSingleLicense(license) {
        try {
            const salt = crypto.randomBytes(16);
            const iv = crypto.randomBytes(16);
            const key = crypto.pbkdf2Sync(this.getEncryptionKey(), salt, 10000, 32, 'sha256');

            const compressed = zlib.gzipSync(JSON.stringify(license));
            const cipher = crypto.createCipheriv(this.algorithm, key, iv);
            let encrypted = cipher.update(compressed, null, 'base64');
            encrypted += cipher.final('base64');

            return { data: encrypted, salt: salt.toString('base64'), iv: iv.toString('base64') };
        } catch (error) {
            throw new Error(`激活码加密失败: ${error.message}`);
        }
    }

    // 解密单个激活码 - 极简版
    decryptSingleLicense(encryptedData) {
        try {
            const salt = Buffer.from(encryptedData.salt, 'base64');
            const iv = Buffer.from(encryptedData.iv, 'base64');
            const key = crypto.pbkdf2Sync(this.getEncryptionKey(), salt, 10000, 32, 'sha256');

            const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
            let decrypted = decipher.update(encryptedData.data, 'base64');
            decrypted = Buffer.concat([decrypted, decipher.final()]);

            const decompressed = zlib.gunzipSync(decrypted);
            return JSON.parse(decompressed.toString('utf8'));
        } catch (error) {
            throw new Error(`激活码解密失败: ${error.message}`);
        }
    }

    // 解密激活码列表
    decryptLicenseList(encryptedData) {
        try {
            const password = this.getEncryptionKey();
            const salt = Buffer.from(encryptedData.salt, 'base64');
            const iv = Buffer.from(encryptedData.iv, 'base64');
            const key = crypto.pbkdf2Sync(password, salt, 100000, 32, 'sha256');

            // 解密数据
            const decipher = crypto.createDecipheriv(encryptedData.algorithm, key, iv);
            let decrypted = decipher.update(encryptedData.data, 'base64');
            decrypted = Buffer.concat([decrypted, decipher.final()]);

            // 解压数据
            const decompressed = zlib.gunzipSync(decrypted);
            const jsonData = decompressed.toString('utf8');
            const data = JSON.parse(jsonData);

            // 验证校验和
            if (encryptedData.metadata && encryptedData.metadata.checksum) {
                const expectedChecksum = this.generateChecksum(jsonData);
                if (expectedChecksum !== encryptedData.metadata.checksum) {
                    throw new Error('数据校验失败，可能已被篡改');
                }
            }

            return data;
        } catch (error) {
            throw new Error(`激活码列表解密失败: ${error.message}`);
        }
    }

    // 获取加密密钥
    getEncryptionKey() {
        const config = require('./s3-config');
        const key = config.get().encryptionKey;
        if (!key) {
            throw new Error('加密密钥未配置');
        }
        return key;
    }

    // 生成数据校验和
    generateChecksum(data) {
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    // 测试功能
    test() {
        console.log('🔐 === 思源笔记激活码系统测试 ===\n');

        // 测试激活码生成
        console.log('🎲 测试激活码生成...');
        const dragonLicense = this.generateLicense('dragon', 'user123', '恶龙会员', '测试生成');
        const annualLicense = this.generateLicense('annual', 'user456', '年付会员', '测试生成');
        const trialLicense = this.generateLicense('trial', 'user789', '体验会员', '测试生成');

        console.log(`恶龙会员: ${dragonLicense.code} (密钥: ${dragonLicense.key.substring(0, 8)}...)`);
        console.log(`年付会员: ${annualLicense.code} (密钥: ${annualLicense.key.substring(0, 8)}...)`);
        console.log(`体验会员: ${trialLicense.code} (密钥: ${trialLicense.key.substring(0, 8)}...)`);

        // 测试激活码解析
        console.log('\n📋 测试激活码解析...');
        try {
            const parsed = this.parseActivationCode(dragonLicense.code);
            console.log(`解析结果: 类型=${parsed.licenseType}`);
            if (parsed.timeInfo) {
                console.log(`时间信息: ${parsed.timeInfo.year}-${parsed.timeInfo.month}-${parsed.timeInfo.day} ${parsed.timeInfo.hour}:${parsed.timeInfo.minute}:${parsed.timeInfo.second}`);
            }
        } catch (error) {
            console.error('解析失败:', error.message);
        }

        // 测试激活码验证
        console.log('\n✅ 测试激活码验证...');
        const validation = this.validateActivationCode(dragonLicense.code, 'dragon');
        console.log(`验证结果: ${validation.valid ? '✅ 通过' : '❌ 失败'} - ${validation.message}`);

        // 测试不同类型的激活码
        console.log('\n🔍 测试激活码安全性...');
        console.log('激活码外观分析:');
        console.log(`恶龙: ${dragonLicense.code} (看不出规律)`);
        console.log(`年付: ${annualLicense.code} (看不出规律)`);
        console.log(`体验: ${trialLicense.code} (看不出规律)`);
        console.log('✅ 激活码完全随机化，无法从外观判断类型或时间');

        console.log('\n🎯 测试完成！');
    }
}

// 导出单例实例
module.exports = new EncryptionManager();
