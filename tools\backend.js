/**
 * 🚀 思源笔记激活码管理后端
 * 
 * 功能特性:
 * - 激活码生成 (单个/批量)
 * - 激活码管理 (查看/搜索/过滤/状态更新)
 * - 七牛云同步 (上传/下载/测试连接)
 * - 配置管理 (动态修改七牛云配置)
 * - 数据加密 (AES-256-CBC + PBKDF2)
 * - 数据导出 (多种格式支持)
 * 
 * 启动方式:
 * node backend.js [port]
 */

const express = require('express');
const path = require('path');
const fs = require('fs');
const qiniu = require('qiniu');

// 导入配置和加密模块
const config = require('./s3-config');
const encryption = require('./encryption');
const S3Manager = require('./s3-client');

class LicenseBackend {
    constructor(port = 3000) {
        this.port = port;
        this.app = express();
        this.licensesFile = path.join(__dirname, 'license-data', 'licenses.json');
        this.licenses = this.loadLicenses();
        
        this.initQiniu();
        this.setupMiddleware();
        this.setupRoutes();
    }

    // 初始化七牛云
    initQiniu() {
        const qiniuConfig = config.get();
        qiniu.conf.ACCESS_KEY = qiniuConfig.accessKey;
        qiniu.conf.SECRET_KEY = qiniuConfig.secretKey;
        
        this.qiniuConfig = new qiniu.conf.Config();
        this.qiniuConfig.zone = qiniu.zone[qiniuConfig.region];
        this.qiniuConfig.useHttpsDomain = true;
        
        this.mac = new qiniu.auth.digest.Mac(qiniuConfig.accessKey, qiniuConfig.secretKey);
        this.bucketManager = new qiniu.rs.BucketManager(this.mac, this.qiniuConfig);
        this.config = qiniuConfig;

        // 初始化S3客户端
        this.s3 = new S3Manager({
            endpoint: this.config.endpoint,
            accessKey: this.config.accessKey,
            secretKey: this.config.secretKey,
            bucket: this.config.bucket,
            region: this.config.region
        });
    }

    // 设置中间件
    setupMiddleware() {
        this.app.use(express.json());
        this.app.use(express.static(path.join(__dirname, 'public')));
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
            res.header('Access-Control-Allow-Headers', 'Content-Type');
            next();
        });
    }

    // 设置路由
    setupRoutes() {
        // 静态页面
        this.app.get('/', (req, res) => res.sendFile(path.join(__dirname, 'public', 'index.html')));
        
        // API路由
        this.app.get('/api/licenses', (req, res) => res.json({ success: true, licenses: this.licenses }));
        this.app.get('/api/config', (req, res) => res.json({ success: true, config: config.get() }));
        this.app.post('/api/config', (req, res) => this.updateConfig(req, res));
        this.app.get('/api/test-connection', (req, res) => this.testConnection(req, res));
        
        this.app.post('/api/generate', (req, res) => this.generateLicense(req, res));
        this.app.post('/api/batch-generate', (req, res) => this.batchGenerate(req, res));
        this.app.post('/api/upload-qiniu', (req, res) => this.uploadToQiniu(req, res));
        this.app.post('/api/download-qiniu', (req, res) => this.downloadFromQiniu(req, res)); // 🔥 智能同步逻辑

        // S3管理路由
        this.app.get('/api/s3/list', (req, res) => this.getS3List(req, res));
        this.app.get('/api/s3/download/:key', (req, res) => this.downloadS3File(req, res));
        this.app.delete('/api/s3/delete/:key', (req, res) => this.deleteS3File(req, res));
        
        this.app.post('/api/mark-used/:id', (req, res) => this.markAsUsed(req, res));
        this.app.post('/api/mark-active/:id', (req, res) => this.markAsActive(req, res));
        this.app.delete('/api/license/:id', (req, res) => this.deleteLicense(req, res));
        this.app.post('/api/batch-update-status', (req, res) => this.batchUpdateStatus(req, res));
        this.app.post('/api/batch-delete', (req, res) => this.batchDelete(req, res));
        
        this.app.get('/api/export/:format', (req, res) => this.exportLicenses(req, res));
    }

    // 激活码目录
    get licenseDir() { return path.join(__dirname, 'license-data'); }

    // 加载所有激活码
    loadLicenses() {
        try {
            if (!fs.existsSync(this.licenseDir)) fs.mkdirSync(this.licenseDir, { recursive: true });
            return fs.readdirSync(this.licenseDir)
                .filter(f => f.endsWith('.json'))
                .map(f => JSON.parse(fs.readFileSync(path.join(this.licenseDir, f), 'utf8')))
                .filter(Boolean);
        } catch { return []; }
    }

    // 保存激活码 - 🔥 支持专属文件名
    saveLicense(license, oldFileName = null) {
        try {
            if (!fs.existsSync(this.licenseDir)) fs.mkdirSync(this.licenseDir, { recursive: true });

            // 🔥 根据激活状态确定文件名
            let fileName;
            if (license.status === 'active' && license.userId && !license.userId.startsWith('batch_')) {
                // 已激活：使用专属文件名
                const cleanUserName = license.userName.replace(/[^\w\u4e00-\u9fa5]/g, '');
                fileName = `${license.code}_${license.userId}_${cleanUserName}.json`;
            } else {
                // 未激活：使用原始文件名
                fileName = `${license.code}.json`;
            }

            const filePath = path.join(this.licenseDir, fileName);
            fs.writeFileSync(filePath, JSON.stringify(license, null, 2));

            // 🔥 如果文件名改变了，删除旧文件
            if (oldFileName && oldFileName !== fileName) {
                const oldPath = path.join(this.licenseDir, oldFileName);
                try { fs.unlinkSync(oldPath); } catch {}
            }

        } catch (error) {
            console.warn('保存激活码失败:', error.message);
        }
    }

    // 删除激活码文件 - 🔥 支持专属文件名
    deleteLicenseFile(code) {
        try {
            // 尝试删除原始文件名
            const originalPath = path.join(this.licenseDir, `${code}.json`);
            if (fs.existsSync(originalPath)) {
                fs.unlinkSync(originalPath);
                return;
            }

            // 🔥 尝试删除专属文件名（遍历查找）
            const files = fs.readdirSync(this.licenseDir);
            const targetFile = files.find(f => f.startsWith(`${code}_`) && f.endsWith('.json'));
            if (targetFile) {
                fs.unlinkSync(path.join(this.licenseDir, targetFile));
            }
        } catch (error) {
            console.warn(`删除激活码文件失败: ${code}`, error.message);
        }
    }

    // 生成激活码
    generateLicense(req, res) {
        try {
            const { userId, userName, licenseType, notes = '' } = req.body;
            if (!userId || !userName || !licenseType) return res.json({ success: false, error: '缺少必需参数' });

            const license = encryption.generateLicense(licenseType, userId, userName, notes);
            this.saveLicense(license);
            this.licenses.push(license);
            res.json({ success: true, license });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 批量生成激活码
    batchGenerate(req, res) {
        try {
            const { licenseType, count } = req.body;
            if (!licenseType || !count || count < 1 || count > 100) return res.json({ success: false, error: '参数错误' });

            const licenses = Array.from({ length: count }, (_, i) => {
                const license = encryption.generateLicense(licenseType, `batch_user_${i + 1}`, `批量用户_${i + 1}`, '批量生成');
                this.saveLicense(license);
                return license;
            });

            this.licenses.push(...licenses);
            res.json({ success: true, licenses });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 更新配置
    updateConfig(req, res) {
        try {
            const result = config.update(req.body);
            if (result.success) {
                this.initQiniu(); // 重新初始化七牛云
                res.json({ success: true, message: '配置更新成功，已重新初始化七牛云连接' });
            } else {
                res.json(result);
            }
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 测试七牛云连接
    async testConnection(req, res) {
        try {
            await new Promise((resolve, reject) => {
                this.bucketManager.listPrefix(this.config.bucket, { limit: 1 }, (err, respBody, respInfo) => {
                    if (err) reject(err);
                    else if (respInfo.statusCode === 200) resolve();
                    else reject(new Error(`HTTP ${respInfo.statusCode}`));
                });
            });
            res.json({ success: true, message: '七牛云连接测试成功' });
        } catch (error) {
            res.json({ success: false, error: `连接失败: ${error.message}` });
        }
    }

    // 上传到七牛云 - 使用S3 API
    async uploadToQiniu(req, res) {
        try {
            // 检查是否是单个激活码上传
            const { singleCode } = req.body;

            if (singleCode) {
                // 单个激活码上传
                const license = this.licenses.find(l => l.code === singleCode);
                if (!license) {
                    return res.json({ success: false, error: '激活码不存在' });
                }

                const fileName = `${license.code}.dat`;
                const encryptedData = encryption.encryptSingleLicense(license);
                await this.s3.upload(fileName, JSON.stringify(encryptedData));

                return res.json({ success: true, message: `${singleCode} 上传成功` });
            }

            // 批量上传
            const existingFiles = await this.s3.list();
            const existingKeys = new Set(existingFiles.map(item => item.Key));

            let uploaded = 0, skipped = 0;

            for (const license of this.licenses) {
                const fileName = `${license.code}.dat`;

                if (existingKeys.has(fileName)) {
                    skipped++;
                    continue;
                }

                const encryptedData = encryption.encryptSingleLicense(license);
                await this.s3.upload(fileName, JSON.stringify(encryptedData));
                uploaded++;
            }

            res.json({ success: true, message: `上传完成: ${uploaded}个新增, ${skipped}个跳过` });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 🔄 智能同步 - 通过激活码名称对比替换
    async downloadFromQiniu(req, res) {
        try {
            console.log('开始智能同步...');

            // 获取S3文件列表
            const files = await this.s3.list();
            const datFiles = files.filter(f => f.Key.endsWith('.dat'));
            console.log(`找到 ${datFiles.length} 个云端文件`);

            // 🔥 分类文件：原始激活码 vs 专属激活码（包含用户信息）
            const originalFiles = datFiles.filter(f => !f.Key.includes('_'));
            const activatedFiles = datFiles.filter(f => f.Key.includes('_'));

            console.log(`原始文件: ${originalFiles.length}, 专属文件: ${activatedFiles.length}`);

            let downloaded = 0, updated = 0, skipped = 0;
            const localCodes = new Set(this.licenses.map(l => l.code));

            // 🔥 第一步：处理专属激活码，替换本地对应的原始激活码
            for (const file of activatedFiles) {
                const code = file.Key.split('_')[0]; // 提取激活码部分
                console.log(`处理专属激活码: ${code}`);

                try {
                    const encryptedDataStr = await this.s3.download(file.Key);
                    const encryptedData = JSON.parse(encryptedDataStr);
                    const license = encryption.decryptSingleLicense(encryptedData);

                    if (localCodes.has(code)) {
                        // 🔥 找到本地对应的原始激活码，用专属激活码替换
                        const index = this.licenses.findIndex(l => l.code === code);
                        if (index !== -1) {
                            console.log(`替换本地激活码: ${code} -> 专属格式`);
                            const oldFileName = `${code}.json`;
                            this.licenses[index] = license;
                            this.saveLicense(license, oldFileName); // 🔥 传入旧文件名，自动处理重命名
                            updated++;
                        }
                    } else {
                        // 本地没有这个激活码，直接添加
                        console.log(`添加新的专属激活码: ${code}`);
                        this.licenses.push(license);
                        this.saveLicense(license);
                        downloaded++;
                    }
                    localCodes.add(code);
                } catch (error) {
                    console.warn(`处理专属激活码 ${file.Key} 失败:`, error.message);
                }
            }

            // 🔥 第二步：处理原始激活码，只下载本地没有的
            for (const file of originalFiles) {
                const code = file.Key.replace('.dat', '');

                if (localCodes.has(code)) {
                    skipped++;
                    continue;
                }

                try {
                    console.log(`下载新的原始激活码: ${code}`);
                    const encryptedDataStr = await this.s3.download(file.Key);
                    const encryptedData = JSON.parse(encryptedDataStr);
                    const license = encryption.decryptSingleLicense(encryptedData);
                    this.licenses.push(license);
                    this.saveLicense(license);
                    downloaded++;
                } catch (error) {
                    console.warn(`下载原始激活码 ${file.Key} 失败:`, error.message);
                }
            }

            const message = `智能同步完成: ${downloaded}个新增, ${updated}个更新, ${skipped}个跳过`;
            console.log(message);

            res.json({ success: true, message });
        } catch (error) {
            console.error('智能同步失败:', error);
            res.json({ success: false, error: error.message });
        }
    }

    // 更新激活码状态
    updateLicenseStatus(req, res, status, message) {
        const license = this.licenses.find(l => l.userId === req.params.id);
        if (!license) return res.json({ success: false, error: '激活码不存在' });
        license.status = status;
        this.saveLicense(license);
        res.json({ success: true, message });
    }

    // 标记为已使用
    markAsUsed(req, res) { this.updateLicenseStatus(req, res, 'used', '已标记为已使用'); }

    // 标记为有效
    markAsActive(req, res) { this.updateLicenseStatus(req, res, 'active', '已重新激活'); }

    // 删除激活码
    deleteLicense(req, res) {
        const index = this.licenses.findIndex(l => l.userId === req.params.id);
        if (index === -1) return res.json({ success: false, error: '激活码不存在' });
        const license = this.licenses.splice(index, 1)[0];
        this.deleteLicenseFile(license.code);
        res.json({ success: true, message: '激活码已删除' });
    }

    // 批量更新状态
    batchUpdateStatus(req, res) {
        const { ids, status } = req.body;
        const updated = ids.reduce((count, id) => {
            const license = this.licenses.find(l => l.userId === id);
            if (license) { license.status = status; this.saveLicense(license); return count + 1; }
            return count;
        }, 0);
        res.json({ success: true, message: `批量更新成功: ${updated}个` });
    }

    // 批量删除
    batchDelete(req, res) {
        const { ids } = req.body;
        this.licenses.filter(l => ids.includes(l.userId)).forEach(l => this.deleteLicenseFile(l.code));
        this.licenses = this.licenses.filter(l => !ids.includes(l.userId));
        res.json({ success: true, message: `批量删除成功: ${ids.length}个` });
    }

    // 导出激活码
    exportLicenses(req, res) {
        const { format } = req.params;
        const { type } = req.query;

        let licenses = this.licenses;
        if (type) {
            // 通过解析激活码获取类型信息进行过滤
            licenses = licenses.filter(l => {
                try {
                    const parsed = encryption.parseActivationCode(l.code);
                    return parsed.licenseType === type;
                } catch {
                    return false;
                }
            });
        }

        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `licenses_${timestamp}.${format === 'csv' ? 'csv' : 'txt'}`;

        let content = '';
        if (format === 'csv') {
            content = '激活码,用户ID,用户名,设备数,状态,备注,密钥\n';
            content += licenses.map(l =>
                `${l.code},${l.userId},${l.userName},${l.maxDevices},${l.status},${l.notes},${l.key.substring(0, 8)}...`
            ).join('\n');
        } else {
            content = licenses.map(l => l.code).join('\n');
        }

        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Content-Type', 'text/plain; charset=utf-8');
        res.send(content);
    }

    // 启动服务器
    start() {
        this.app.listen(this.port, () => {
            console.log('🎯 思源笔记激活码管理中心 - 极简版');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log(`🚀 服务器启动成功: http://localhost:${this.port}`);
            console.log(`📊 当前激活码数量: ${this.licenses.length}`);
            console.log(`☁️  S3存储空间: ${this.config.bucket}`);
            console.log(`🔐 加密密钥: SiYuan_F...`);
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
            console.log('💡 核心特性:');
            console.log('   • 🎲 24位激活码 - 完全随机，无规律可循');
            console.log('   • 🔑 随机密钥 - 每个激活码独立64位密钥');
            console.log('   • 📋 极简结构 - 7个字段，独立JSON文件');
            console.log('   • ☁️  S3云同步 - 每个激活码独立加密压缩包');
            console.log('   • 🔄 批量操作 - 生成/管理/上传/下载');
            console.log('   • ✨ 优雅完美 - 极限精简，简洁高效');
            console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
        });
    }

    // ==================== S3管理API ====================

    // 获取S3文件列表
    async getS3List(req, res) {
        try {
            const files = await this.s3.list();
            res.json({ success: true, files });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 下载单个S3文件
    async downloadS3File(req, res) {
        try {
            const key = decodeURIComponent(req.params.key);
            const data = await this.s3.download(key);

            if (key.endsWith('.dat')) {
                // 激活码文件，解密并保存
                const encryptedData = JSON.parse(data);
                const license = encryption.decryptSingleLicense(encryptedData);
                this.licenses.push(license);
                this.saveLicense(license);
                res.json({ success: true, message: `激活码文件 ${key} 下载并保存成功` });
            } else {
                // 其他文件，直接返回内容
                res.json({ success: true, data, message: `文件 ${key} 下载成功` });
            }
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }

    // 删除S3文件
    async deleteS3File(req, res) {
        try {
            const key = decodeURIComponent(req.params.key);
            await this.s3.delete(key);
            res.json({ success: true, message: `文件 ${key} 删除成功` });
        } catch (error) {
            res.json({ success: false, error: error.message });
        }
    }
}

// 启动服务器
const port = process.argv[2] || 3000;
const backend = new LicenseBackend(port);
backend.start();
