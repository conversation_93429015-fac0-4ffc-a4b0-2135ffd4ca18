<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活码管理中心</title>
    <style>
        /* 基础重置 */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: system-ui, sans-serif; background: #f5f5f5; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }

        /* 布局组件 */
        .header { background: #f8f9fa; border-bottom: 1px solid #e9ecef; padding: 1rem; text-align: center; }
        .header h1 { font-size: 1.5rem; color: #333; margin-bottom: 0.5rem; }
        .header p { color: #666; }
        .tabs-nav { display: flex; background: #f8f9fa; border-bottom: 1px solid #e9ecef; }
        .tab-btn { flex: 1; padding: 1rem; border: none; background: none; cursor: pointer; color: #666; border-bottom: 2px solid transparent; }
        .tab-btn.active { color: #007bff; border-bottom-color: #007bff; background: white; }
        .tab-content { display: none; padding: 1.5rem; }
        .tab-content.active { display: block; }
        .grid { display: grid; gap: 1.5rem; }
        .grid-2 { grid-template-columns: 1fr 1fr; }
        .grid-4 { grid-template-columns: repeat(4, 1fr); }
        .panel { background: #f8f9fa; border-radius: 6px; padding: 1.5rem; }
        .panel h2, .panel h3 { color: #333; margin-bottom: 1rem; }

        /* 表单组件 */
        .form-group { margin-bottom: 1rem; }
        .form-group label { display: block; margin-bottom: 0.5rem; color: #555; }
        input, select, textarea { width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; }
        input:focus, select:focus, textarea:focus { outline: none; border-color: #007bff; }

        /* 按钮组件 */
        .btn { background: #007bff; color: white; border: none; padding: 0.75rem 1rem; border-radius: 4px; cursor: pointer; margin: 0.25rem; transition: background-color 0.2s; }
        .btn:hover { background: #0056b3; }
        .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.75rem; min-width: 50px; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #5a6268; }
        .btn-copy { background: #28a745; }
        .btn-copy:hover { background: #218838; }
        .btn-used { background: #ffc107; color: #212529; }
        .btn-used:hover { background: #e0a800; }
        .btn-activate { background: #28a745; }
        .btn-activate:hover { background: #218838; }
        .btn-delete { background: #dc3545; }
        .btn-delete:hover { background: #c82333; }
        .btn-sync { background: #17a2b8; }
        .btn-sync:hover { background: #138496; }

        /* 状态组件 */
        .result-panel { margin-top: 1rem; padding: 1rem; border-radius: 4px; display: none; }
        .result-success { background: #d4edda; color: #155724; }
        .result-error { background: #f8d7da; color: #721c24; }

        /* 统计组件 */
        .stat-card { background: #007bff; color: white; padding: 1rem; border-radius: 4px; text-align: center; }
        .stat-card .number { font-size: 1.5rem; font-weight: bold; }
        .stat-card .label { font-size: 0.875rem; opacity: 0.9; }
        .stat-item { color: #666; }
        .stat-value { font-weight: bold; color: #007bff; margin-left: 0.5rem; }
        .stats-summary { display: flex; gap: 1rem; flex-wrap: wrap; align-items: center; }
        .missing-count { color: #c62828 !important; }

        /* 控件组件 */
        .controls { display: flex; gap: 1rem; align-items: center; margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 6px; flex-wrap: wrap; }
        .control-group { display: flex; flex-direction: column; gap: 0.25rem; }
        .control-group label { font-size: 0.875rem; font-weight: 500; color: #555; }
        .control-group input, .control-group select { padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px; font-size: 0.875rem; }
        .batch-controls { background: #e3f2fd; padding: 1rem; border-radius: 6px; margin-bottom: 1rem; display: flex; gap: 1rem; align-items: center; border-left: 4px solid #2196f3; }

        /* 列表组件 */
        .list-header { background: #f8f9fa; padding: 1rem; border-bottom: 1px solid #e9ecef; }
        .list-header h3 { margin: 0; }
        .group-header { background: #e9ecef; padding: 0.75rem 1rem; font-weight: bold; border-bottom: 2px solid #dee2e6; display: flex; justify-content: space-between; align-items: center; }
        .list-item { padding: 1.25rem; border-bottom: 1px solid #e9ecef; display: grid; gap: 1rem; align-items: center; transition: background-color 0.2s; }
        .list-item:hover { background: #f8f9fa; }
        .list-item.selected { background: #e3f2fd; border-left: 4px solid #2196f3; }
        .item-code { font-family: 'Courier New', monospace; font-weight: bold; font-size: 0.9rem; color: #333; }
        .item-meta { font-size: 0.875rem; color: #666; margin-top: 0.25rem; }

        /* 标签组件 */
        .badge { padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.75rem; font-weight: 500; text-align: center; }
        .badge-dragon { background: #fff3cd; color: #856404; }
        .badge-annual { background: #d4edda; color: #155724; }
        .badge-trial { background: #cce5ff; color: #004085; }
        .badge-active { background: #d4edda; color: #155724; }
        .badge-used { background: #f8d7da; color: #721c24; }
        .badge-unused { background: #28a745; color: white; }
        .badge-expired { background: #6c757d; color: white; }

        /* 操作组件 */
        .actions { display: flex; gap: 0.25rem; justify-content: flex-end; align-items: center; }

        /* 比较组件 */
        .compare-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 4px; }
        .compare-container { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; height: 600px; }
        .compare-panel { border: 1px solid #ddd; border-radius: 4px; overflow: hidden; }
        .compare-panel h3 { margin: 0; padding: 0.75rem; background: #f8f9fa; border-bottom: 1px solid #ddd; font-size: 0.875rem; }
        .compare-stats { padding: 0.75rem; background: #fafafa; border-bottom: 1px solid #ddd; font-size: 0.875rem; }
        .compare-list { height: calc(100% - 100px); overflow-y: auto; }
        .compare-item { padding: 0.5rem 0.75rem; border-bottom: 1px solid #eee; font-family: monospace; font-size: 0.875rem; display: flex; justify-content: space-between; align-items: center; min-height: 60px; gap: 1rem; }
        .compare-item:last-child { border-bottom: none; }
        .compare-item.missing { background: #ffebee; color: #c62828; }
        .compare-item.empty { background: #f5f5f5; color: #999; border: 2px dashed #ddd; }
        .compare-item.same { color: #2e7d32; }
        .compare-item > div:first-child { flex: 1; min-width: 0; }
        .compare-code { font-weight: bold; word-break: break-all; }
        .compare-meta { font-size: 0.75rem; color: #666; }
        .compare-actions { display: flex; gap: 0.25rem; flex-shrink: 0; align-items: center; }

        /* 对话框组件 */
        .dialog-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 1000; }
        .dialog { background: white; border-radius: 8px; padding: 2rem; min-width: 400px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .dialog-info { background: #f8f9fa; padding: 1rem; border-radius: 4px; margin: 1rem 0; }
        .dialog-actions { display: flex; gap: 1rem; margin-top: 1.5rem; justify-content: flex-end; }

        /* 工具组件 */
        .empty-state { padding: 2rem; text-align: center; color: #999; }
        .loading { text-align: center; padding: 2rem; color: #666; }
        .toast { position: fixed; top: 20px; right: 20px; padding: 12px 20px; border-radius: 4px; color: white; z-index: 9999; font-weight: 500; }
        .toast.success { background: #28a745; }
        .toast.error { background: #dc3545; }

        /* 响应式 */
        @media (max-width: 768px) {
            .grid-2, .grid-4 { grid-template-columns: 1fr; }
            .controls { flex-direction: column; align-items: stretch; }
            .list-item { grid-template-columns: 1fr; gap: 0.5rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>激活码管理中心</h1>
            <p>激活码生成 • 管理 • 导出 • 云端同步</p>
        </div>
        
        <div class="tabs-nav">
            <button class="tab-btn active" data-tab="generate">生成激活码</button>
            <button class="tab-btn" data-tab="manage">管理激活码</button>
            <button class="tab-btn" data-tab="s3">云端存储</button>
            <button class="tab-btn" data-tab="settings">系统设置</button>
        </div>
        
        <div id="generate" class="tab-content active">
            <div class="grid grid-2">
                <div class="panel">
                    <h2>生成激活码</h2>
                    <form id="generateForm">
                        <div class="form-group">
                            <label>思源用户ID *</label>
                            <input type="text" name="userId" required placeholder="用户的思源账号ID">
                        </div>
                        <div class="form-group">
                            <label>用户名 *</label>
                            <input type="text" name="userName" required placeholder="用户显示名称">
                        </div>
                        <div class="form-group">
                            <label>会员类型 *</label>
                            <select name="licenseType" required>
                                <option value="">请选择会员类型</option>
                                <option value="dragon">恶龙会员 (永久)</option>
                                <option value="annual">年付会员 (365天)</option>
                                <option value="trial">体验会员 (7天)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>最大设备数</label>
                            <input type="number" name="maxDevices" value="3" min="1" max="10">
                        </div>
                        <div class="form-group">
                            <label>备注</label>
                            <textarea name="notes" rows="3" placeholder="可选的备注信息"></textarea>
                        </div>
                        <button type="submit" class="btn">生成激活码</button>
                    </form>
                    <div id="generateResult" class="result-panel"></div>
                </div>
                <div class="panel">
                    <h2>批量生成</h2>
                    <form id="batchGenerateForm">
                        <div class="form-group">
                            <label>会员类型 *</label>
                            <select name="licenseType" required>
                                <option value="">请选择类型</option>
                                <option value="dragon">恶龙会员</option>
                                <option value="annual">年付会员</option>
                                <option value="trial">体验会员</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>生成数量 *</label>
                            <input type="number" name="count" required min="1" max="100" placeholder="1-100">
                        </div>
                        <button type="submit" class="btn">批量生成</button>
                    </form>
                    <div id="batchResult" class="result-panel"></div>
                </div>
            </div>
        </div>
        
        <div id="manage" class="tab-content">
            <div class="grid grid-4">
                <div class="stat-card">
                    <div class="number" id="totalCount">0</div>
                    <div class="label">总激活码</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="dragonCount">0</div>
                    <div class="label">恶龙会员</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="annualCount">0</div>
                    <div class="label">年付会员</div>
                </div>
                <div class="stat-card">
                    <div class="number" id="trialCount">0</div>
                    <div class="label">体验会员</div>
                </div>
            </div>

            <div class="list-header">
                <h3>激活码管理</h3>
            </div>

            <div class="controls">
                <div class="control-group">
                    <label>搜索激活码</label>
                    <input type="text" id="searchInput" placeholder="输入激活码或用户名">
                </div>
                <div class="control-group">
                    <label>会员类型</label>
                    <select id="typeFilter">
                        <option value="">全部类型</option>
                        <option value="dragon">恶龙会员</option>
                        <option value="annual">年付会员</option>
                        <option value="trial">体验会员</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>时间范围</label>
                    <select id="timeFilter">
                        <option value="">全部时间</option>
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                    </select>
                </div>
                <div class="control-group">
                    <label><input type="checkbox" id="groupByType"> 分组显示</label>
                </div>
                <button class="btn btn-secondary" onclick="App.refreshLicenses()">刷新</button>
                <button class="btn btn-secondary" onclick="App.showExportDialog()">导出</button>
            </div>

            <div class="batch-controls" id="batchControls" style="display: none;">
                <span id="selectedCount">已选择 0 项</span>
                <button class="btn btn-secondary" onclick="App.batchMarkUsed()">批量标记已用</button>
                <button class="btn btn-secondary" onclick="App.batchMarkActive()">批量激活</button>
                <button class="btn btn-secondary" onclick="App.batchDelete()">批量删除</button>
                <button class="btn btn-secondary" onclick="App.clearSelection()">取消选择</button>
            </div>
            <div id="licensesList"></div>
        </div>
        
        <div id="s3" class="tab-content">
            <div class="compare-header">
                <div class="stats-summary">
                    <span class="stat-item">本地: <span id="localFileCount" class="stat-value">-</span></span>
                    <span class="stat-item">云端: <span id="s3FileCount" class="stat-value">-</span></span>
                </div>
                <div class="actions">
                    <button class="btn btn-sync" onclick="App.syncFromS3()">智能同步</button>
                    <button class="btn" onclick="App.uploadToS3()">上传</button>
                    <button class="btn" onclick="App.downloadFromS3()">下载</button>
                    <button class="btn btn-secondary" onclick="App.refreshS3List()">刷新</button>
                </div>
            </div>
            <div id="s3Result" class="result-panel"></div>

            <div class="controls">
                <div class="control-group">
                    <label>搜索激活码</label>
                    <input type="text" id="compareSearch" placeholder="搜索激活码..." onkeyup="App.filterCompareList()">
                </div>
                <div class="control-group">
                    <label>会员类型</label>
                    <select id="compareTypeFilter" onchange="App.filterCompareList()">
                        <option value="all">全部类型</option>
                        <option value="dragon">龙年版</option>
                        <option value="annual">年度版</option>
                        <option value="trial">试用版</option>
                    </select>
                </div>
                <div class="control-group">
                    <label>同步状态</label>
                    <select id="compareStatusFilter" onchange="App.filterCompareList()">
                        <option value="all">全部状态</option>
                        <option value="both">两端都有</option>
                        <option value="local-only">仅本地</option>
                        <option value="s3-only">仅云端</option>
                        <option value="missing">有缺失</option>
                    </select>
                </div>
                <button class="btn btn-secondary" onclick="App.clearCompareFilters()">清除筛选</button>
            </div>

            <div class="compare-container">
                <div class="compare-panel">
                    <h3>本地激活码</h3>
                    <div class="compare-stats">
                        <div class="stats-summary">
                            <span>总计: <strong id="localTotalCount">0</strong></span>
                            <span>缺失: <strong id="localMissingCount" class="missing-count">0</strong></span>
                            <span id="localStatsTypes"></span>
                        </div>
                    </div>
                    <div id="localFilesList" class="compare-list" onscroll="App.syncScroll(this, 's3FilesList')"></div>
                </div>
                <div class="compare-panel">
                    <h3>云端激活码</h3>
                    <div class="compare-stats">
                        <div class="stats-summary">
                            <span>总计: <strong id="s3TotalCount">0</strong></span>
                            <span>缺失: <strong id="s3MissingCount" class="missing-count">0</strong></span>
                            <span id="s3StatsTypes"></span>
                        </div>
                    </div>
                    <div id="s3FilesList" class="compare-list" onscroll="App.syncScroll(this, 'localFilesList')"></div>
                </div>
            </div>
        </div>

        <div id="settings" class="tab-content">
            <div class="grid grid-2">
                <div class="panel">
                    <h2>S3存储配置</h2>
                    <form id="configForm">
                        <div class="form-group">
                            <label>存储提供商</label>
                            <select id="s3Provider" onchange="App.updateS3Config()">
                                <option value="qiniu">七牛云 (Qiniu)</option>
                                <option value="aliyun">阿里云 (Aliyun OSS)</option>
                                <option value="tencent">腾讯云 (COS)</option>
                                <option value="aws">亚马逊 (AWS S3)</option>
                                <option value="minio">MinIO</option>
                                <option value="custom">自定义</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>AccessKey *</label>
                            <input type="text" name="accessKey" id="configAccessKey" required placeholder="S3访问密钥">
                        </div>
                        <div class="form-group">
                            <label>SecretKey *</label>
                            <input type="text" name="secretKey" id="configSecretKey" required placeholder="S3私钥">
                        </div>
                        <div class="form-group">
                            <label>存储桶名称 *</label>
                            <input type="text" name="bucket" id="configBucket" required placeholder="S3存储桶名称">
                        </div>
                        <div class="form-group">
                            <label>区域 *</label>
                            <input type="text" name="region" id="configRegion" required placeholder="存储区域 (如: cn-south-1)">
                        </div>
                        <div class="form-group">
                            <label>端点地址</label>
                            <input type="url" name="endpoint" id="configEndpoint" placeholder="S3端点地址 (如: https://s3.cn-south-1.qiniucs.com)">
                        </div>
                        <div class="form-group">
                            <label>加密密钥</label>
                            <input type="text" name="encryptionKey" id="configEncryptionKey" placeholder="激活码数据加密密钥，请妥善保管">
                        </div>
                        <div class="actions">
                            <button type="submit" class="btn">保存配置</button>
                            <button type="button" class="btn btn-secondary" onclick="App.loadConfig()">重新加载</button>
                            <button type="button" class="btn btn-secondary" onclick="App.testConnection()">测试连接</button>
                        </div>
                    </form>
                    <div id="configResult" class="result-panel"></div>
                </div>
                <div class="panel">
                    <h2>配置说明</h2>
                    <div style="font-size: 0.875rem; color: #666; line-height: 1.6;">
                        <h4>获取七牛云配置信息：</h4>
                        <p><strong>AccessKey & SecretKey：</strong>登录七牛云控制台 → 密钥管理 → 创建密钥</p>
                        <p><strong>存储空间：</strong>登录七牛云控制台 → 对象存储 → 创建空间</p>
                        <p><strong>存储区域：</strong>选择离用户最近的区域以获得最佳访问速度</p>
                        <p><strong>访问域名：</strong>可使用默认域名或绑定自定义域名</p>
                        <h4>安全提醒：</h4>
                        <p>• 请妥善保管AccessKey和SecretKey，避免泄露</p>
                        <p>• 加密密钥用于保护激活码数据，请使用强密码</p>
                        <p>• 建议定期更换密钥以确保安全</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 导出对话框 -->
    <div id="exportDialog" class="dialog-overlay" style="display: none;" onclick="App.hideExportDialog()">
        <div class="dialog" onclick="event.stopPropagation()">
            <h3>导出激活码</h3>
            <div class="dialog-info">
                <p>导出范围: <span id="exportRange">全部类型</span></p>
                <p>数量: <span id="exportCount">0</span> 个</p>
            </div>
            <div class="form-group">
                <label>导出格式:</label>
                <select id="exportFormat">
                    <option value="card">发码平台格式 (.txt)</option>
                    <option value="csv">CSV表格格式 (.csv)</option>
                    <option value="json">JSON数据格式 (.json)</option>
                    <option value="txt">纯文本格式 (.txt)</option>
                </select>
            </div>
            <div class="dialog-actions">
                <button class="btn btn-secondary" onclick="App.hideExportDialog()">取消</button>
                <button class="btn" onclick="App.exportLicenses()">下载</button>
            </div>
        </div>
    </div>
    
    <script src="app.js"></script>
</body>
</html>
